<!DOCTYPE html>
<html>
<head>
  <title>VidCompressor</title>
  <style>
    body {
      font-family: sans-serif;
      width: 400px;
    }
    #videos {
      margin-top: 10px;
    }
    .video-item {
      display: flex;
      justify-content: space-between;
      padding: 5px;
      border-bottom: 1px solid #ccc;
    }

    /* Modal styles */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: none;
      z-index: 1000;
    }

    .modal-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      border-radius: 12px;
      padding: 24px;
      width: 320px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }

    .modal-header {
      text-align: center;
      margin-bottom: 20px;
    }

    .modal-title {
      font-size: 18px;
      font-weight: 500;
      margin: 0;
      color: #333;
    }

    .compression-options {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .compression-option {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.1s ease;
      background: white;
    }

    .compression-option:hover {
      background-color: #f5f5f5;
      border-color: #1976d2;
    }

    .compression-option.selected {
      background-color: #e3f2fd;
      border-color: #1976d2;
      box-shadow: 0 0 0 1px #1976d2;
    }

    .option-info {
      flex: 1;
    }

    .option-title {
      font-weight: 500;
      color: #333;
      margin-bottom: 2px;
    }

    .option-description {
      font-size: 12px;
      color: #666;
      line-height: 1.3;
    }

    .option-size {
      font-size: 11px;
      color: #888;
      margin-top: 2px;
    }

    .modal-actions {
      display: flex;
      gap: 12px;
      margin-top: 24px;
      justify-content: flex-end;
    }

    .modal-button {
      padding: 8px 16px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: background-color 0.1s ease;
    }

    .modal-button.cancel {
      background: #f5f5f5;
      color: #666;
    }

    .modal-button.cancel:hover {
      background: #e0e0e0;
    }

    .modal-button.compress {
      background: #1976d2;
      color: white;
    }

    .modal-button.compress:hover {
      background: #1565c0;
    }

    .modal-button.compress:disabled {
      background: #ccc;
      cursor: not-allowed;
    }
  </style>
</head>
<body>
  <h1>VidCompressor</h1>
  <div id="auth-container">
    <button id="login">Login with Google</button>
  </div>
  <div id="main-container" style="display: none;">
    <button id="logout">Logout</button>
    <div id="videos-container">
      <h2>Your Videos</h2>
      <div id="videos-list"></div>
    </div>
    <div id="status"></div>
  </div>

  <!-- Compression Modal -->
  <div id="compression-modal" class="modal-overlay">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title">Choose compression quality</h3>
      </div>
      <div class="compression-options">
        <div class="compression-option" data-quality="high">
          <div class="option-info">
            <div class="option-title">High Quality</div>
            <div class="option-description">Best quality, larger file size</div>
            <div class="option-size" id="high-size">Estimated size: ~</div>
          </div>
        </div>
        <div class="compression-option" data-quality="medium">
          <div class="option-info">
            <div class="option-title">Medium Quality</div>
            <div class="option-description">Balanced quality and file size</div>
            <div class="option-size" id="medium-size">Estimated size: ~</div>
          </div>
        </div>
        <div class="compression-option" data-quality="low">
          <div class="option-info">
            <div class="option-title">Low Quality</div>
            <div class="option-description">Smaller file size, reduced quality</div>
            <div class="option-size" id="low-size">Estimated size: ~</div>
          </div>
        </div>
      </div>
      <div class="modal-actions">
        <button class="modal-button cancel" id="cancel-compression">Cancel</button>
        <button class="modal-button compress" id="confirm-compression" disabled>Compress</button>
      </div>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
