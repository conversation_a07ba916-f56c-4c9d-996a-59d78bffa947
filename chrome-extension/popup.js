document.addEventListener('DOMContentLoaded', function() {
  const loginButton = document.getElementById('login');
  const logoutButton = document.getElementById('logout');
  const authContainer = document.getElementById('auth-container');
  const mainContainer = document.getElementById('main-container');
  const videosList = document.getElementById('videos-list');
  const statusDiv = document.getElementById('status');
  const compressionModal = document.getElementById('compression-modal');
  const cancelButton = document.getElementById('cancel-compression');
  const confirmButton = document.getElementById('confirm-compression');

  let authToken = null;
  let selectedVideo = null;
  let selectedQuality = null;

  // Check for existing token on startup
  chrome.identity.getAuthToken({ interactive: false }, function(token) {
    if (chrome.runtime.lastError) {
      // Not logged in
      return;
    }
    authToken = token;
    showMainContainer();
    fetchVideos();
  });

  loginButton.addEventListener('click', function() {
    chrome.identity.getAuthToken({ interactive: true }, function(token) {
      if (chrome.runtime.lastError) {
        statusDiv.textContent = 'Login failed: ' + chrome.runtime.lastError.message;
        return;
      }
      authToken = token;
      showMainContainer();
      fetchVideos();
    });
  });

  logoutButton.addEventListener('click', function() {
    if (authToken) {
      chrome.identity.removeCachedAuthToken({ token: authToken }, function() {
        authToken = null;
        showAuthContainer();
      });
    }
  });

  function showAuthContainer() {
    authContainer.style.display = 'block';
    mainContainer.style.display = 'none';
  }

  function showMainContainer() {
    authContainer.style.display = 'none';
    mainContainer.style.display = 'block';
  }

  function fetchVideos() {
    fetch('http://localhost:5000/api/videos', {
      headers: {
        'Authorization': 'Bearer ' + authToken
      }
    })
    .then(response => response.json())
    .then(videos => {
      videosList.innerHTML = '';
      videos.forEach(video => {
        const videoItem = document.createElement('div');
        videoItem.className = 'video-item';

        const videoName = document.createElement('span');
        videoName.textContent = video;
        videoItem.appendChild(videoName);

        const compressButton = document.createElement('button');
        compressButton.textContent = 'Compress';
        compressButton.addEventListener('click', function() {
          showCompressionModal(video);
        });
        videoItem.appendChild(compressButton);

        videosList.appendChild(videoItem);
      });
    })
    .catch(error => {
      statusDiv.textContent = 'Error fetching videos: ' + error.message;
    });
  }

  function showCompressionModal(video) {
    selectedVideo = video;
    selectedQuality = null;

    // Calculate estimated file sizes (mock calculation for now)
    const originalSize = Math.floor(Math.random() * 100) + 50; // Mock original size in MB
    document.getElementById('high-size').textContent = `Estimated size: ~${Math.floor(originalSize * 0.8)}MB`;
    document.getElementById('medium-size').textContent = `Estimated size: ~${Math.floor(originalSize * 0.5)}MB`;
    document.getElementById('low-size').textContent = `Estimated size: ~${Math.floor(originalSize * 0.3)}MB`;

    // Reset selection
    document.querySelectorAll('.compression-option').forEach(option => {
      option.classList.remove('selected');
    });
    confirmButton.disabled = true;

    // Show modal
    compressionModal.style.display = 'block';
  }

  function hideCompressionModal() {
    compressionModal.style.display = 'none';
    selectedVideo = null;
    selectedQuality = null;
  }

  function compressVideo(mediaItemId, quality) {
    statusDiv.textContent = `Compressing ${mediaItemId} with ${quality} quality...`;
    fetch(`http://localhost:5000/api/videos/${mediaItemId}/compress`, {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + authToken,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ quality: quality })
    })
    .then(response => response.json())
    .then(data => {
      statusDiv.textContent = data.message;
    })
    .catch(error => {
      statusDiv.textContent = 'Error compressing video: ' + error.message;
    });
  }

  // Modal event listeners
  document.querySelectorAll('.compression-option').forEach(option => {
    option.addEventListener('click', function() {
      // Immediate visual feedback - remove delay
      document.querySelectorAll('.compression-option').forEach(opt => {
        opt.classList.remove('selected');
      });
      this.classList.add('selected');

      selectedQuality = this.dataset.quality;
      confirmButton.disabled = false;
    });
  });

  cancelButton.addEventListener('click', hideCompressionModal);

  confirmButton.addEventListener('click', function() {
    if (selectedVideo && selectedQuality) {
      hideCompressionModal();
      compressVideo(selectedVideo, selectedQuality);
    }
  });

  // Close modal when clicking outside
  compressionModal.addEventListener('click', function(e) {
    if (e.target === compressionModal) {
      hideCompressionModal();
    }
  });
});
